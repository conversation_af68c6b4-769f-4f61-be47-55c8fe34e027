import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:imtrans/services/local_ocr_service.dart';

/// Service for managing JavaScript-based text overlays in WebView
/// Handles injection, positioning, and cleanup of translated text overlays
class WebViewOverlayService {
  static final WebViewOverlayService _instance = WebViewOverlayService._internal();
  factory WebViewOverlayService() => _instance;
  WebViewOverlayService._internal();

  bool _isInitialized = false;
  bool _overlaysActive = false;
  InAppWebViewController? _currentController;
  final List<String> _activeOverlayIds = [];

  /// Initialize the overlay service
  Future<void> initialize() async {
    try {
      _isInitialized = true;
      debugPrint('WebViewOverlayService: Initialized successfully');
    } catch (e) {
      debugPrint('WebViewOverlayService: Failed to initialize - $e');
      throw OverlayException('Failed to initialize overlay service: $e');
    }
  }

  /// Check if the service is initialized
  bool get isInitialized => _isInitialized;

  /// Check if overlays are currently active
  bool get overlaysActive => _overlaysActive;

  /// Set the current WebView controller
  void setController(InAppWebViewController? controller) {
    _currentController = controller;
  }

  /// Inject CSS styles for overlays
  Future<void> _injectOverlayStyles() async {
    if (_currentController == null) return;

    const cssStyles = '''
      <style id="translation-overlay-styles">
        .translation-overlay {
          position: absolute;
          background-color: rgba(255, 255, 255, 0.9);
          //border: 1px solid #CDEE2D;
          border-radius: 4px;
          padding: 2px 4px;
          //font-family: , 'Segoe UI', Roboto, sans-serif;
          //font-weight: 300;
          color: #333;
          z-index: 10000;
          pointer-events: none;
          box-shadow: 0 2px 4px rgba(0,0,0,0.1);
          transition: opacity 0.2s ease;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          max-width: 300px;
        }
        
        .translation-overlay.fade-in {
          opacity: 1;
        }
        
        .translation-overlay.fade-out {
          opacity: 0;
        }
        
        .translation-overlay-container {
          position: relative;
          z-index: 9999;
        }

        .translation-loading-overlay {
          position: absolute;
          background-color: rgba(0, 0, 0, 0.6);
          color: white;
          z-index: 9999;
          pointer-events: none;
          display: flex;
          align-items: center;
          justify-content: center;
          flex-direction: column;
          font-size: 14px;
          font-weight: 500;
          border-radius: 8px;
          backdrop-filter: blur(2px);
          -webkit-backdrop-filter: blur(2px);
        }

        .translation-loading-spinner {
          width: 24px;
          height: 24px;
          border: 2px solid rgba(255, 255, 255, 0.3);
          border-top: 2px solid white;
          border-radius: 50%;
          animation: spin 1s linear infinite;
          margin-bottom: 8px;
        }

        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }

        .image-overlay-container {
          position: relative;
          display: inline-block;
        }

        .translation-action-button {
          position: absolute;
          top: 8px;
          right: 8px;
          width: 36px;
          height: 36px;
          background-color: rgba(0, 123, 255, 0.9);
          color: white;
          border: none;
          border-radius: 50%;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 16px;
          font-weight: bold;
          z-index: 10001;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
          transition: all 0.2s ease;
          opacity: 0.8;
        }

        .translation-action-button:hover {
          opacity: 1;
          transform: scale(1.1);
          background-color: rgba(0, 123, 255, 1);
        }

        .translation-action-button.processing {
          background-color: rgba(255, 193, 7, 0.9);
          cursor: not-allowed;
        }

        .translation-action-button.completed {
          background-color: rgba(40, 167, 69, 0.9);
        }
      </style>
    ''';

    try {
      await _currentController!.evaluateJavascript(source: '''
        if (!document.getElementById('translation-overlay-styles')) {
          document.head.insertAdjacentHTML('beforeend', `$cssStyles`);
        }
      ''');
    } catch (e) {
      debugPrint('WebViewOverlayService: Failed to inject styles - $e');
    }
  }

  /// Inject JavaScript functions for overlay management
  Future<void> _injectOverlayScripts() async {
    if (_currentController == null) return;

    const jsScript = '''
      window.translationOverlays = window.translationOverlays || {
        overlays: new Map(),
        loadingOverlays: new Map(),
        actionButtons: new Map(),
        scrollHandler: null,
        intersectionObserver: null,
        imageCache: new Map(),
        processingImages: new Set(),
        translationModeEnabled: false,

        // Initialize intersection observer for lazy processing
        initIntersectionObserver: function() {
          if (this.intersectionObserver) return;

          this.intersectionObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
              if (entry.isIntersecting) {
                const img = entry.target;
                const imageUrl = img.src;

                // Filter out small icons and decorative images
                if (!this.isMainContentImage(img)) {
                  return;
                }

                // Check if image is already processed or being processed
                if (!this.imageCache.has(imageUrl) && !this.processingImages.has(imageUrl)) {
                  console.log('Main content image entered viewport, ready for processing:', imageUrl);
                  // Trigger processing event that Flutter can listen to
                  if (window.flutter_inappwebview && window.flutter_inappwebview.callHandler) {
                    window.flutter_inappwebview.callHandler('onImageVisible', imageUrl);
                  }
                }
              }
            });
          }, {
            root: null,
            rootMargin: '50px',
            threshold: 0.1
          });

          // Observe only main content images
          document.querySelectorAll('img').forEach(img => {
            if (this.isMainContentImage(img)) {
              this.intersectionObserver.observe(img);
              console.log('Observing main content image:', img.src);
            }
          });
        },

        // Check if image is likely main content (not icon/decoration)
        isMainContentImage: function(img) {
          const url = img.src.toLowerCase();
          const alt = (img.alt || '').toLowerCase();

          // Skip if no src or blob URLs
          if (!url || url.startsWith('blob:') || url.startsWith('data:')) {
            return false;
          }

          // Skip common icon/decoration keywords
          const skipKeywords = [
            'logo', 'icon', 'favicon', 'avatar', 'thumb', 'thumbnail',
            'button', 'arrow', 'star', 'heart', 'like', 'share',
            'comment', 'menu', 'header', 'footer', 'banner',
            'advertisement', 'sponsor', 'widget', 'brand',
            'interface', 'control', 'social', 'badge'
          ];

          for (const keyword of skipKeywords) {
            if (url.includes(keyword) || alt.includes(keyword)) {
              return false;
            }
          }

          // Check image dimensions
          const width = img.naturalWidth || img.width || 0;
          const height = img.naturalHeight || img.height || 0;

          // Skip images that are too small (likely icons)
          if (width < 100 || height < 100) {
            return false;
          }

          // Skip images with unusual aspect ratios (likely decorative)
          //if (width > 0 && height > 0) {
          //  const aspectRatio = width / height;
          //  if (aspectRatio > 8 || aspectRatio < 0.125) {
          //    return false;
          //  }
          //}

          // Prefer images with content-related keywords
          const contentKeywords = [
            'manga', 'comic', 'chapter', 'page', 'content', 'main',
            'story', 'episode', 'part', 'vol', 'volume', 'data',
            'webtoon', 'manhwa', 'manhua'
          ];

          //for (const keyword of contentKeywords) {
          //  if (url.includes(keyword) || alt.includes(keyword)) {
          //    return true;
          //  }
          //}

          // If image is reasonably sized and not obviously decorative, include it
          //return width >= 200 && height >= 200;
          return true;
        },

        // Show loading indicator on image
        showLoadingIndicator: function(imageUrl, loadingText) {
          const images = document.querySelectorAll('img');
          let targetImage = null;

          for (let img of images) {
            if (img.src === imageUrl || img.src.includes(imageUrl.split('/').pop())) {
              targetImage = img;
              break;
            }
          }

          if (!targetImage) return;

          this.processingImages.add(imageUrl);

          const imageRect = targetImage.getBoundingClientRect();
          const scrollX = window.scrollX;
          const scrollY = window.scrollY;

          const loadingOverlay = document.createElement('div');
          loadingOverlay.id = 'loading-overlay-' + Date.now();
          loadingOverlay.className = 'translation-loading-overlay';

          // Create spinner and text
          const spinner = document.createElement('div');
          spinner.className = 'translation-loading-spinner';

          const text = document.createElement('div');
          text.textContent = loadingText || 'Translating...';

          loadingOverlay.appendChild(spinner);
          loadingOverlay.appendChild(text);

          // Cover the entire image with semi-transparent overlay
          loadingOverlay.style.left = (imageRect.left + scrollX) + 'px';
          loadingOverlay.style.top = (imageRect.top + scrollY) + 'px';
          loadingOverlay.style.width = imageRect.width + 'px';
          loadingOverlay.style.height = imageRect.height + 'px';

          document.body.appendChild(loadingOverlay);
          this.loadingOverlays.set(imageUrl, loadingOverlay);

          // Set a timeout to auto-hide loading indicator after 30 seconds
          setTimeout(() => {
            if (this.loadingOverlays.has(imageUrl)) {
              console.log('Auto-hiding loading indicator after timeout for:', imageUrl);
              this.hideLoadingIndicator(imageUrl);
            }
          }, 30000); // 30 seconds timeout

          console.log('Loading indicator shown for:', imageUrl);
        },

        // Hide loading indicator
        hideLoadingIndicator: function(imageUrl) {
          console.log('Attempting to hide loading indicator for:', imageUrl);

          // Try exact match first
          let loadingOverlay = this.loadingOverlays.get(imageUrl);
          if (loadingOverlay) {
            loadingOverlay.remove();
            this.loadingOverlays.delete(imageUrl);
            console.log('Loading indicator removed (exact match) for:', imageUrl);
          } else {
            // Try partial match if exact match fails
            let foundKey = null;
            this.loadingOverlays.forEach((overlay, key) => {
              if (key.includes(imageUrl.split('/').pop()) || imageUrl.includes(key.split('/').pop())) {
                loadingOverlay = overlay;
                foundKey = key;
              }
            });

            if (loadingOverlay && foundKey) {
              loadingOverlay.remove();
              this.loadingOverlays.delete(foundKey);
              console.log('Loading indicator removed (partial match) for:', foundKey, '-> requested:', imageUrl);
            } else {
              console.warn('Loading indicator not found for:', imageUrl, 'Available keys:', Array.from(this.loadingOverlays.keys()));
            }
          }

          this.processingImages.delete(imageUrl);
          console.log('Loading indicator hidden for:', imageUrl);
        },

        // Force hide all loading indicators (cleanup function)
        hideAllLoadingIndicators: function() {
          console.log('Force hiding all loading indicators, count:', this.loadingOverlays.size);

          // Remove from Map
          this.loadingOverlays.forEach((overlay, imageUrl) => {
            if (overlay && overlay.parentNode) {
              overlay.remove();
            }
          });
          this.loadingOverlays.clear();
          this.processingImages.clear();

          // Also search and remove any orphaned loading overlays in DOM
          const orphanedLoadingOverlays = document.querySelectorAll('.translation-loading-overlay');
          orphanedLoadingOverlays.forEach(overlay => {
            console.log('Removing orphaned loading overlay:', overlay);
            overlay.remove();
          });

          console.log('All loading indicators cleared, removed', orphanedLoadingOverlays.length, 'orphaned overlays');
        },

        // Debug function to check loading indicator status
        debugLoadingIndicators: function() {
          console.log('=== Loading Indicators Debug ===');
          console.log('loadingOverlays Map size:', this.loadingOverlays.size);
          console.log('processingImages Set size:', this.processingImages.size);

          this.loadingOverlays.forEach((overlay, imageUrl) => {
            console.log('Loading overlay for:', imageUrl, 'exists in DOM:', !!overlay.parentNode);
          });

          const domLoadingOverlays = document.querySelectorAll('.translation-loading-overlay');
          console.log('DOM loading overlays count:', domLoadingOverlays.length);

          domLoadingOverlays.forEach((overlay, index) => {
            console.log('DOM loading overlay', index, ':', overlay);
          });

          console.log('=== End Debug ===');
        },

        createImageOverlay: function(id, text, imageUrl, x, y, width, height, fontSize) {
          console.log('=== Creating overlay ===');
          console.log('ID:', id);
          console.log('Text:', text);
          console.log('ImageURL:', imageUrl);
          console.log('Position:', x, y, width, height);
          console.log('Current overlays count:', this.overlays.size);

          // Check if overlay with this ID already exists in Map
          if (this.overlays.has(id)) {
            console.log('WARNING: Overlay with ID', id, 'already exists in Map, skipping creation');
            console.log('Existing overlay:', this.overlays.get(id));
            return this.overlays.get(id).element;
          }

          // Also check if DOM element with this ID already exists
          const existingDomElement = document.getElementById('translation-overlay-' + id);
          if (existingDomElement) {
            console.log('WARNING: DOM element with ID translation-overlay-' + id + ' already exists, removing it');
            existingDomElement.remove();
          }

          // Force hide all loading indicators first to ensure cleanup
          this.hideAllLoadingIndicators();

          // Also specifically try to hide for this image URL
          this.hideLoadingIndicator(imageUrl);

          // Find the image element
          const images = document.querySelectorAll('img');
          let targetImage = null;

          for (let img of images) {
            console.log('Checking image:', img.src);
            if (img.src === imageUrl || img.src.includes(imageUrl.split('/').pop())) {
              targetImage = img;
              console.log('Found target image:', img.src);
              break;
            }
          }

          if (!targetImage) {
            console.warn('Target image not found for overlay:', imageUrl);
            console.log('Available images:', Array.from(images).map(img => img.src));
            // Still hide loading indicator even if image not found
            this.hideLoadingIndicator(imageUrl);
            // Fallback to absolute positioning
            return this.createOverlay(id, text, imageUrl, x, y, width, height, fontSize);
          }

          // Wait for image to load if necessary
          if (!targetImage.complete || targetImage.naturalWidth === 0) {
            console.log('Image not loaded yet, waiting...');
            targetImage.onload = () => {
              this.createImageOverlay(id, text, imageUrl, x, y, width, height, fontSize);
            };
            // Set a timeout to hide loading indicator if image fails to load
            setTimeout(() => {
              this.hideLoadingIndicator(imageUrl);
            }, 10000); // 10 second timeout
            return null;
          }

          // Get image position and size
          const imageRect = targetImage.getBoundingClientRect();
          const scrollX = window.scrollX;
          const scrollY = window.scrollY;

          console.log('Image rect:', imageRect);
          console.log('Natural size:', targetImage.naturalWidth, targetImage.naturalHeight);

          // Calculate scale factors - ensure image coordinates are relative to image bounds
          const scaleX = imageRect.width / targetImage.naturalWidth;
          const scaleY = imageRect.height / targetImage.naturalHeight;

          // Calculate absolute position - constrain within image boundaries
          const relativeX = Math.max(0, Math.min(x, targetImage.naturalWidth));
          const relativeY = Math.max(0, Math.min(y, targetImage.naturalHeight));

          const absoluteX = imageRect.left + scrollX + (relativeX * scaleX);
          const absoluteY = imageRect.top + scrollY + (relativeY * scaleY);

          // Ensure overlay stays within image bounds
          const maxX = imageRect.left + scrollX + imageRect.width - 10;
          const maxY = imageRect.top + scrollY + imageRect.height - 10;

          const constrainedX = Math.min(absoluteX, maxX);
          const constrainedY = Math.min(absoluteY, maxY);

          console.log('Constrained position:', constrainedX, constrainedY);

          const overlay = document.createElement('div');
          overlay.id = 'translation-overlay-' + id;
          overlay.className = 'translation-overlay';
          overlay.textContent = text;
          overlay.style.left = constrainedX + 'px';
          overlay.style.top = constrainedY + 'px';
          overlay.style.fontSize = fontSize + 'px';

          // Set overlay dimensions to match OCR detected area
          const overlayWidth = Math.max(width * scaleX, 100); // Minimum width of 100px
          const overlayHeight = Math.max(height * scaleY, fontSize * 1.2); // Minimum height based on font size

          // Ensure overlay doesn't exceed image bounds
          const maxOverlayWidth = Math.min(overlayWidth, imageRect.width - (constrainedX - imageRect.left - scrollX));
          const maxOverlayHeight = Math.min(overlayHeight, imageRect.height - (constrainedY - imageRect.top - scrollY));

          overlay.style.width = maxOverlayWidth + 'px';
          overlay.style.height = maxOverlayHeight + 'px';
          overlay.style.maxWidth = 'none'; // Remove max-width constraint
          overlay.style.minWidth = Math.min(overlayWidth, 100) + 'px'; // Ensure minimum readable width
          overlay.style.zIndex = '10000';

          // Ensure text wraps properly within the overlay area
          overlay.style.wordWrap = 'break-word';
          overlay.style.overflowWrap = 'break-word';
          overlay.style.whiteSpace = 'normal';
          overlay.style.overflow = 'hidden';
          overlay.style.display = 'flex';
          overlay.style.alignItems = 'center';
          overlay.style.justifyContent = 'center';
          overlay.style.textAlign = 'center';

          console.log('Overlay dimensions:', {
            width: maxOverlayWidth,
            height: maxOverlayHeight,
            originalOcrWidth: width,
            originalOcrHeight: height,
            scaleX: scaleX,
            scaleY: scaleY
          });

          document.body.appendChild(overlay);
          this.overlays.set(id, {
            element: overlay,
            targetImage: targetImage,
            originalX: relativeX,
            originalY: relativeY,
            scrollX: scrollX,
            scrollY: scrollY,
            imageUrl: imageUrl
          });

          // Mark image as processed in cache
          this.imageCache.set(imageUrl, true);

          // Update action button state to completed
          this.updateActionButtonState(imageUrl, 'completed');

          console.log('=== Overlay creation completed ===');
          console.log('Overlay ID:', id);
          console.log('Overlay element:', overlay);
          console.log('Total overlays now:', this.overlays.size);
          console.log('All overlay IDs:', Array.from(this.overlays.keys()));

          // Fade in animation
          setTimeout(() => {
            overlay.classList.add('fade-in');
            console.log('Fade in animation applied for overlay:', id);
          }, 10);

          return overlay;
        },

        createOverlay: function(id, text, imageUrl, x, y, width, height, fontSize) {
          // Check if overlay with this ID already exists
          if (this.overlays.has(id)) {
            console.log('Overlay with ID', id, 'already exists, skipping creation');
            return this.overlays.get(id).element;
          }

          // Fallback to absolute positioning
          const overlay = document.createElement('div');
          overlay.id = 'translation-overlay-' + id;
          overlay.className = 'translation-overlay';
          overlay.textContent = text;
          overlay.style.left = x + 'px';
          overlay.style.top = y + 'px';
          overlay.style.fontSize = fontSize + 'px';

          // Set dimensions to match OCR detected area
          const overlayWidth = Math.max(width, 100); // Minimum width of 100px
          const overlayHeight = Math.max(height, fontSize * 1.2); // Minimum height based on font size

          overlay.style.width = overlayWidth + 'px';
          overlay.style.height = overlayHeight + 'px';
          overlay.style.minWidth = Math.min(overlayWidth, 100) + 'px';
          overlay.style.maxWidth = 'none'; // Remove max-width constraint

          // Ensure text wraps properly within the overlay area
          overlay.style.wordWrap = 'break-word';
          overlay.style.overflowWrap = 'break-word';
          overlay.style.whiteSpace = 'normal';
          overlay.style.overflow = 'hidden';
          overlay.style.display = 'flex';
          overlay.style.alignItems = 'center';
          overlay.style.justifyContent = 'center';
          overlay.style.textAlign = 'center';

          document.body.appendChild(overlay);
          this.overlays.set(id, {
            element: overlay,
            originalX: x,
            originalY: y,
            scrollX: window.scrollX,
            scrollY: window.scrollY,
            imageUrl: imageUrl
          });

          // Update action button state to completed
          this.updateActionButtonState(imageUrl, 'completed');

          // Fade in animation
          setTimeout(() => overlay.classList.add('fade-in'), 10);

          return overlay;
        },
        
        removeOverlay: function(id) {
          const overlayData = this.overlays.get(id);
          if (overlayData) {
            overlayData.element.classList.add('fade-out');
            setTimeout(() => {
              if (overlayData.element.parentNode) {
                overlayData.element.parentNode.removeChild(overlayData.element);
              }
              this.overlays.delete(id);
            }, 200);
          }
        },
        
        removeAllOverlays: function() {
          // Collect all image URLs that have overlays before removing them
          const imageUrlsWithOverlays = new Set();
          this.overlays.forEach((overlayData, id) => {
            if (overlayData.imageUrl) {
              imageUrlsWithOverlays.add(overlayData.imageUrl);
            }
            this.removeOverlay(id);
          });
          this.overlays.clear();

          // Reset action button states for images that had overlays
          imageUrlsWithOverlays.forEach(imageUrl => {
            this.updateActionButtonState(imageUrl, 'ready');
          });

          console.log('Removed all overlays and reset', imageUrlsWithOverlays.size, 'action button states');
        },
        
        updateOverlayPositions: function() {
          const currentScrollX = window.scrollX;
          const currentScrollY = window.scrollY;

          this.overlays.forEach((overlayData, id) => {
            if (overlayData.targetImage) {
              // Image-relative positioning
              const imageRect = overlayData.targetImage.getBoundingClientRect();
              const absoluteX = imageRect.left + currentScrollX + (overlayData.originalX * imageRect.width / overlayData.targetImage.naturalWidth);
              const absoluteY = imageRect.top + currentScrollY + (overlayData.originalY * imageRect.height / overlayData.targetImage.naturalHeight);

              overlayData.element.style.left = absoluteX + 'px';
              overlayData.element.style.top = absoluteY + 'px';
            } else {
              // Fallback to absolute positioning
              const deltaX = currentScrollX - overlayData.scrollX;
              const deltaY = currentScrollY - overlayData.scrollY;

              overlayData.element.style.left = (overlayData.originalX - deltaX) + 'px';
              overlayData.element.style.top = (overlayData.originalY - deltaY) + 'px';
            }
          });
        },
        
        setupScrollHandler: function() {
          if (this.scrollHandler) return;
          
          this.scrollHandler = () => {
            this.updateOverlayPositions();
          };
          
          window.addEventListener('scroll', this.scrollHandler, { passive: true });
          window.addEventListener('resize', this.scrollHandler, { passive: true });
        },
        
        removeScrollHandler: function() {
          if (this.scrollHandler) {
            window.removeEventListener('scroll', this.scrollHandler);
            window.removeEventListener('resize', this.scrollHandler);
            this.scrollHandler = null;
          }
        },

        // Initialize the system
        initialize: function() {
          this.initIntersectionObserver();
          this.setupScrollHandler();
          console.log('Translation overlay system initialized');
        },

        // Clear cache for specific image
        clearImageCache: function(imageUrl) {
          this.imageCache.delete(imageUrl);
          console.log('Cache cleared for:', imageUrl);
        },

        // Clear all cache
        clearAllCache: function() {
          this.imageCache.clear();
          console.log('All cache cleared');
        },

        // Check if image is cached
        isImageCached: function(imageUrl) {
          return this.imageCache.has(imageUrl);
        },

        // Get processing status
        isImageProcessing: function(imageUrl) {
          return this.processingImages.has(imageUrl);
        },

        // Check if image has translation overlays
        hasImageOverlays: function(imageUrl) {
          let hasOverlays = false;
          this.overlays.forEach((overlayData, id) => {
            if (overlayData.imageUrl === imageUrl) {
              hasOverlays = true;
            }
          });
          return hasOverlays;
        },

        // Remove all overlays for a specific image
        removeImageOverlays: function(imageUrl) {
          const overlaysToRemove = [];
          this.overlays.forEach((overlayData, id) => {
            if (overlayData.imageUrl === imageUrl) {
              overlaysToRemove.push(id);
            }
          });

          overlaysToRemove.forEach(id => {
            this.removeOverlay(id);
          });

          // Update action button state if overlays were removed
          if (overlaysToRemove.length > 0) {
            this.updateActionButtonState(imageUrl, 'ready');
          }

          console.log('Removed', overlaysToRemove.length, 'overlays for image:', imageUrl);
          return overlaysToRemove.length > 0;
        },

        // Enable/disable translation mode
        setTranslationMode: function(enabled, buttonText) {
          this.translationModeEnabled = enabled;
          this.buttonText = buttonText || 'Translate this image';
          if (enabled) {
            this.showAllActionButtons();
          } else {
            this.hideAllActionButtons();
          }
          console.log('Translation mode:', enabled ? 'enabled' : 'disabled');
        },

        // Show action buttons on all eligible images
        showAllActionButtons: function() {
          const images = document.querySelectorAll('img');
          images.forEach(img => {
            if (this.isMainContentImage(img)) {
              this.showActionButton(img);
            }
          });
        },

        // Hide all action buttons
        hideAllActionButtons: function() {
          this.actionButtons.forEach((button, imageUrl) => {
            if (button && button.parentNode) {
              button.parentNode.removeChild(button);
            }
          });
          this.actionButtons.clear();
        },

        // Show action button on specific image
        showActionButton: function(img) {
          const imageUrl = img.src;

          // Don't show button if already exists or image is being processed
          if (this.actionButtons.has(imageUrl) || this.processingImages.has(imageUrl)) {
            return;
          }

          const button = document.createElement('button');
          button.className = 'translation-action-button';
          button.innerHTML = '🌐'; // Globe icon
          button.title = this.buttonText || 'Translate this image';

          // Position button relative to image
          const updateButtonPosition = () => {
            const rect = img.getBoundingClientRect();
            const scrollX = window.scrollX;
            const scrollY = window.scrollY;

            button.style.position = 'absolute';
            button.style.left = (rect.right + scrollX - 44) + 'px'; // 44 = button width + margin
            button.style.top = (rect.top + scrollY + 8) + 'px';
          };

          // Initial positioning
          updateButtonPosition();

          // Handle click
          button.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();

            if (this.processingImages.has(imageUrl)) {
              return; // Already processing
            }

            // Check if image already has overlays (toggle functionality)
            if (this.hasImageOverlays(imageUrl)) {
              // Remove existing overlays
              const removed = this.removeImageOverlays(imageUrl);
              if (removed) {
                // Update button state to ready
                button.classList.remove('processing', 'completed');
                button.innerHTML = '🌐'; // Globe icon
                button.title = this.buttonText || 'Translate this image';
                console.log('Toggled OFF: Removed overlays for', imageUrl);
              }
            } else {
              // Create new translation
              // Update button state
              button.classList.add('processing');
              button.innerHTML = '⏳'; // Hourglass icon
              button.title = 'Translating...';

              // Notify Flutter
              if (window.flutter_inappwebview && window.flutter_inappwebview.callHandler) {
                window.flutter_inappwebview.callHandler('onTranslateImageRequested', imageUrl);
                console.log('Toggled ON: Requesting translation for', imageUrl);
              }
            }
          });

          // Update position on scroll/resize
          const positionHandler = () => updateButtonPosition();
          window.addEventListener('scroll', positionHandler);
          window.addEventListener('resize', positionHandler);

          // Store cleanup function
          button._cleanup = () => {
            window.removeEventListener('scroll', positionHandler);
            window.removeEventListener('resize', positionHandler);
          };

          document.body.appendChild(button);
          this.actionButtons.set(imageUrl, button);

          console.log('Action button shown for:', imageUrl);
        },

        // Update action button state
        updateActionButtonState: function(imageUrl, state) {
          const button = this.actionButtons.get(imageUrl);
          if (!button) return;

          button.classList.remove('processing', 'completed');

          switch (state) {
            case 'processing':
              button.classList.add('processing');
              button.innerHTML = '⏳';
              button.title = 'Translating...';
              break;
            case 'completed':
              // Check if image actually has overlays
              if (this.hasImageOverlays(imageUrl)) {
                button.classList.add('completed');
                button.innerHTML = '✓';
                button.title = 'Translation completed - click to hide';
              } else {
                // No overlays, reset to ready state
                button.innerHTML = '🌐';
                button.title = this.buttonText || 'Translate this image';
              }
              break;
            case 'ready':
            default:
              button.innerHTML = '🌐';
              button.title = this.buttonText || 'Translate this image';
              break;
          }
        },

        // Remove action button for specific image
        removeActionButton: function(imageUrl) {
          const button = this.actionButtons.get(imageUrl);
          if (button) {
            if (button._cleanup) {
              button._cleanup();
            }
            if (button.parentNode) {
              button.parentNode.removeChild(button);
            }
            this.actionButtons.delete(imageUrl);
          }
        }
      };
    ''';

    try {
      await _currentController!.evaluateJavascript(source: jsScript);
    } catch (e) {
      debugPrint('WebViewOverlayService: Failed to inject scripts - $e');
    }
  }

  /// High-level method to show translation overlays
  Future<void> showTranslationOverlays({
    required List<OcrTextElement> translatedElements,
    required List<String> imageUrls,
  }) async {
    return showOverlays(translatedElements, imageUrls);
  }

  /// Initialize the overlay system with intersection observer
  Future<void> initializeOverlaySystem() async {
    if (_currentController == null) return;

    try {
      await _injectOverlayStyles();
      await _injectOverlayScripts();

      await _currentController!.evaluateJavascript(
        source: 'window.translationOverlays.initialize();'
      );

      debugPrint('WebViewOverlayService: Overlay system initialized');
    } catch (e) {
      debugPrint('WebViewOverlayService: Failed to initialize overlay system - $e');
    }
  }

  /// Show loading indicator for a specific image
  Future<void> showLoadingIndicator(String imageUrl, {String? loadingText}) async {
    if (_currentController == null) return;

    final text = loadingText ?? 'Translating...';

    try {
      await _currentController!.evaluateJavascript(source: '''
        window.translationOverlays.showLoadingIndicator('$imageUrl', '$text');
      ''');
      debugPrint('WebViewOverlayService: Loading indicator shown for $imageUrl');
    } catch (e) {
      debugPrint('WebViewOverlayService: Failed to show loading indicator - $e');
    }
  }

  /// Hide loading indicator for a specific image
  Future<void> hideLoadingIndicator(String imageUrl) async {
    if (_currentController == null) return;

    try {
      await _currentController!.evaluateJavascript(source: '''
        window.translationOverlays.hideLoadingIndicator('$imageUrl');
      ''');
      debugPrint('WebViewOverlayService: Loading indicator hidden for $imageUrl');
    } catch (e) {
      debugPrint('WebViewOverlayService: Failed to hide loading indicator - $e');
    }
  }

  /// Force hide all loading indicators (cleanup function)
  Future<void> hideAllLoadingIndicators() async {
    if (_currentController == null) return;

    try {
      await _currentController!.evaluateJavascript(source: '''
        window.translationOverlays.hideAllLoadingIndicators();
      ''');
      debugPrint('WebViewOverlayService: All loading indicators hidden');
    } catch (e) {
      debugPrint('WebViewOverlayService: Failed to hide all loading indicators - $e');
    }
  }

  /// Debug loading indicators status
  Future<void> debugLoadingIndicators() async {
    if (_currentController == null) return;

    try {
      await _currentController!.evaluateJavascript(source: '''
        window.translationOverlays.debugLoadingIndicators();
      ''');
    } catch (e) {
      debugPrint('WebViewOverlayService: Failed to debug loading indicators - $e');
    }
  }

  /// Check if image is already cached/processed
  Future<bool> isImageCached(String imageUrl) async {
    if (_currentController == null) return false;

    try {
      final result = await _currentController!.evaluateJavascript(source: '''
        window.translationOverlays.isImageCached('$imageUrl');
      ''');
      return result == true;
    } catch (e) {
      debugPrint('WebViewOverlayService: Failed to check cache status - $e');
      return false;
    }
  }

  /// Check if image is currently being processed
  Future<bool> isImageProcessing(String imageUrl) async {
    if (_currentController == null) return false;

    try {
      final result = await _currentController!.evaluateJavascript(source: '''
        window.translationOverlays.isImageProcessing('$imageUrl');
      ''');
      return result == true;
    } catch (e) {
      debugPrint('WebViewOverlayService: Failed to check processing status - $e');
      return false;
    }
  }

  /// Clear cache for specific image
  Future<void> clearImageCache(String imageUrl) async {
    if (_currentController == null) return;

    try {
      await _currentController!.evaluateJavascript(source: '''
        window.translationOverlays.clearImageCache('$imageUrl');
      ''');
      debugPrint('WebViewOverlayService: Cache cleared for $imageUrl');
    } catch (e) {
      debugPrint('WebViewOverlayService: Failed to clear image cache - $e');
    }
  }

  /// Clear all translation cache
  Future<void> clearAllCache() async {
    if (_currentController == null) return;

    try {
      await _currentController!.evaluateJavascript(source: '''
        window.translationOverlays.clearAllCache();
      ''');
      debugPrint('WebViewOverlayService: All cache cleared');
    } catch (e) {
      debugPrint('WebViewOverlayService: Failed to clear all cache - $e');
    }
  }

  /// Enable or disable translation mode (shows/hides action buttons)
  Future<void> setTranslationMode(bool enabled, {String? buttonText}) async {
    if (_currentController == null) return;

    final text = buttonText ?? 'Translate this image';

    try {
      await _currentController!.evaluateJavascript(source: '''
        window.translationOverlays.setTranslationMode($enabled, '$text');
      ''');
      debugPrint('WebViewOverlayService: Translation mode ${enabled ? 'enabled' : 'disabled'}');
    } catch (e) {
      debugPrint('WebViewOverlayService: Failed to set translation mode - $e');
    }
  }

  /// Update action button state for a specific image
  Future<void> updateActionButtonState(String imageUrl, String state) async {
    if (_currentController == null) return;

    try {
      await _currentController!.evaluateJavascript(source: '''
        window.translationOverlays.updateActionButtonState('$imageUrl', '$state');
      ''');
      debugPrint('WebViewOverlayService: Updated action button state for $imageUrl to $state');
    } catch (e) {
      debugPrint('WebViewOverlayService: Failed to update action button state - $e');
    }
  }

  /// Remove action button for a specific image
  Future<void> removeActionButton(String imageUrl) async {
    if (_currentController == null) return;

    try {
      await _currentController!.evaluateJavascript(source: '''
        window.translationOverlays.removeActionButton('$imageUrl');
      ''');
      debugPrint('WebViewOverlayService: Removed action button for $imageUrl');
    } catch (e) {
      debugPrint('WebViewOverlayService: Failed to remove action button - $e');
    }
  }

  /// Create and display overlays for translated text elements
  Future<void> showOverlays(List<OcrTextElement> translatedElements, List<String> imageUrls) async {
    if (!_isInitialized || _currentController == null) {
      throw OverlayException('Overlay service not initialized or controller not set');
    }

    try {
      // Initialize the overlay system
      await initializeOverlaySystem();

      // Force hide any remaining loading indicators
      await _currentController!.evaluateJavascript(source: '''
        window.translationOverlays.hideAllLoadingIndicators();
      ''');

      debugPrint('WebViewOverlayService: Creating overlays for ${translatedElements.length} elements and ${imageUrls.length} images');

      // Group elements by image (assuming elements are ordered by image)
      int elementsPerImage = translatedElements.length ~/ imageUrls.length;
      if (elementsPerImage == 0) elementsPerImage = 1;

      debugPrint('WebViewOverlayService: Elements per image: $elementsPerImage');

      // Create overlays for each translated element
      for (int i = 0; i < translatedElements.length; i++) {
        final element = translatedElements[i];
        final overlayId = 'overlay_$i';
        final imageIndex = i ~/ elementsPerImage;
        final imageUrl = imageIndex < imageUrls.length ? imageUrls[imageIndex] : imageUrls.last;

        final boundingBox = element.boundingBox;
        final fontSize = _calculateFontSize(boundingBox.width, boundingBox.height);

        debugPrint('WebViewOverlayService: Creating overlay $overlayId for text "${element.text}" at (${boundingBox.left}, ${boundingBox.top}) on image $imageUrl');

        // Create overlay positioned relative to the specific image
        try {
          await _currentController!.evaluateJavascript(source: '''
            window.translationOverlays.createImageOverlay(
              '$overlayId',
              ${_escapeJavaScriptString(element.text)},
              '$imageUrl',
              ${boundingBox.left},
              ${boundingBox.top},
              ${boundingBox.width},
              ${boundingBox.height},
              $fontSize
            );
          ''');

          _activeOverlayIds.add(overlayId);
          debugPrint('WebViewOverlayService: Successfully created overlay $overlayId');
        } catch (e) {
          debugPrint('WebViewOverlayService: Failed to create overlay $overlayId: $e');
        }
      }

      _overlaysActive = true;
      debugPrint('WebViewOverlayService: Created ${translatedElements.length} overlays for ${imageUrls.length} images');
    } catch (e) {
      debugPrint('WebViewOverlayService: Failed to show overlays - $e');
      throw OverlayException('Failed to show overlays: $e');
    }
  }

  /// Hide all overlays
  Future<void> hideOverlays() async {
    if (!_isInitialized || _currentController == null) return;

    try {
      await _currentController!.evaluateJavascript(
        source: 'window.translationOverlays.removeAllOverlays();'
      );
      
      await _currentController!.evaluateJavascript(
        source: 'window.translationOverlays.removeScrollHandler();'
      );

      _activeOverlayIds.clear();
      _overlaysActive = false;
      debugPrint('WebViewOverlayService: Hidden all overlays');
    } catch (e) {
      debugPrint('WebViewOverlayService: Failed to hide overlays - $e');
    }
  }

  /// Clean up all overlays and handlers
  Future<void> cleanup() async {
    if (!_isInitialized || _currentController == null) return;

    try {
      await hideOverlays();
      
      // Remove injected styles
      await _currentController!.evaluateJavascript(source: '''
        const styleElement = document.getElementById('translation-overlay-styles');
        if (styleElement) {
          styleElement.parentNode.removeChild(styleElement);
        }
      ''');
      
      debugPrint('WebViewOverlayService: Cleanup completed');
    } catch (e) {
      debugPrint('WebViewOverlayService: Cleanup failed - $e');
    }
  }

  /// Calculate appropriate font size based on bounding box dimensions
  double _calculateFontSize(double width, double height) {
    // Base font size calculation based on bounding box height
    double fontSize = height * 0.7; // 70% of bounding box height
    
    // Clamp font size to reasonable bounds
    fontSize = fontSize.clamp(10.0, 18.0);
    
    return fontSize;
  }

  /// Escape JavaScript string to prevent injection attacks
  String _escapeJavaScriptString(String text) {
    return "'" + text
        .replaceAll('\\', '\\\\')
        .replaceAll("'", "\\'")
        .replaceAll('"', '\\"')
        .replaceAll('\n', '\\n')
        .replaceAll('\r', '\\r')
        .replaceAll('\t', '\\t') + "'";
  }

  /// Dispose the service
  void dispose() {
    _currentController = null;
    _activeOverlayIds.clear();
    _overlaysActive = false;
    _isInitialized = false;
    debugPrint('WebViewOverlayService: Disposed');
  }
}

/// Custom exception for overlay operations
class OverlayException implements Exception {
  final String message;
  final dynamic originalError;

  const OverlayException(this.message, [this.originalError]);

  @override
  String toString() {
    if (originalError != null) {
      return 'OverlayException: $message (Original error: $originalError)';
    }
    return 'OverlayException: $message';
  }
}
