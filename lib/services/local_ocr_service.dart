import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:vision_text_recognition/vision_text_recognition.dart';

/// Local OCR service using vision_text_recognition package
/// Provides text recognition from images with comprehensive error handling
class LocalOcrService {
  static final LocalOcrService _instance = LocalOcrService._internal();
  factory LocalOcrService() => _instance;
  LocalOcrService._internal();

  final config = TextRecognitionConfig(
    // recognitionLevel: RecognitionLevel.accurate,
    usesLanguageCorrection: true,
    automaticallyDetectsLanguage: true,
    minimumTextHeight: 0.02,
    // preferredLanguages: ['en', 'es', 'fr'],
  );

  bool _isInitialized = false;

  /// Initialize the OCR service
  /// Must be called before using any OCR functionality
  Future<void> initialize() async {
    try {
      // Check if vision text recognition is available
      final isAvailable = await VisionTextRecognition.isAvailable();
      if (!isAvailable) {
        throw OcrException('Vision text recognition is not available on this platform');
      }

      _isInitialized = true;
      debugPrint('LocalOcrService: Initialized successfully');
    } catch (e) {
      debugPrint('LocalOcrService: Failed to initialize - $e');
      throw OcrException('Failed to initialize OCR service: $e');
    }
  }

  /// Check if the service is initialized
  bool get isInitialized => _isInitialized;

  /// Extract text from image bytes
  /// Returns a list of recognized text elements with their positions
  Future<List<OcrTextElement>> extractTextFromBytes(Uint8List imageBytes, {Size? imageSize}) async {
    if (!_isInitialized) {
      throw OcrException('OCR service not initialized. Call initialize() first.');
    }

    try {
      debugPrint('LocalOcrService: Starting text extraction from image bytes (${imageBytes.length} bytes)');

      // Check if the service is still available
      final isAvailable = await VisionTextRecognition.isAvailable();
      if (!isAvailable) {
        throw OcrException('Vision text recognition is not available');
      }

      // Perform text recognition using the correct API
      final recognizedText = await VisionTextRecognition.recognizeTextWithConfig(imageBytes, config);
      debugPrint('LocalOcrService: Recognition completed, full text length: ${recognizedText.fullText.length}');

      if (recognizedText.fullText.isEmpty) {
        debugPrint('LocalOcrService: No text recognized in image');
        return [];
      }

      debugPrint('LocalOcrService: Full recognized text: "${recognizedText.fullText}"');

      // Convert to our format
      final textElements = <OcrTextElement>[];

      // debugPrint('LocalOcrService: Processing ${recognizedText.textBlocks.length} text blocks');

      // Try to get image dimensions for coordinate conversion
      Size actualImageSize = imageSize ?? const Size(1.0, 1.0);
      if (imageSize == null) {
        // Try to decode image to get actual dimensions
        try {
          actualImageSize = await _getImageSizeFromBytes(imageBytes);
          debugPrint('LocalOcrService: Decoded image size from bytes: ${actualImageSize.width}x${actualImageSize.height}');
        } catch (e) {
          debugPrint('LocalOcrService: Could not decode image size from bytes: $e');
          // Use normalized coordinates as fallback
          actualImageSize = const Size(1.0, 1.0);
          debugPrint('LocalOcrService: Using normalized coordinates as fallback');
        }
      }

      for (int i = 0; i < recognizedText.textBlocks.length; i++) {
        final block = recognizedText.textBlocks[i];
        final boundingBox = block.boundingBox;

        debugPrint('LocalOcrService: Block $i - Text: "${block.text}", Confidence: ${block.confidence}');
        debugPrint('LocalOcrService: Block $i - Normalized BoundingBox: (${boundingBox.x}, ${boundingBox.y}, ${boundingBox.width}, ${boundingBox.height})');

        // Convert normalized coordinates to pixel coordinates
        final left = boundingBox.x * actualImageSize.width;
        final top = boundingBox.y * actualImageSize.height;
        final right = (boundingBox.x + boundingBox.width) * actualImageSize.width;
        final bottom = (boundingBox.y + boundingBox.height) * actualImageSize.height;

        debugPrint('LocalOcrService: Block $i - Pixel BoundingBox: ($left, $top, $right, $bottom)');

        textElements.add(OcrTextElement(
          text: block.text,
          boundingBox: OcrBoundingBox(
            left: left,
            top: top,
            right: right,
            bottom: bottom,
          ),
          confidence: block.confidence,
        ));
      }

      debugPrint('LocalOcrService: Extracted ${textElements.length} text elements');

      // Group nearby text elements into coherent sentences
      final groupedElements = _groupTextElements(textElements);
      debugPrint('LocalOcrService: Text grouping completed: ${groupedElements.length} grouped elements (reduced from ${textElements.length})');

      // return groupedElements;
      return textElements;
    } catch (e) {
      debugPrint('LocalOcrService: Text extraction failed - $e');
      throw OcrException('Failed to extract text from image: $e');
    }
  }

  /// Extract text from image file path
  Future<List<OcrTextElement>> extractTextFromPath(String imagePath) async {
    if (!_isInitialized) {
      throw OcrException('OCR service not initialized. Call initialize() first.');
    }

    try {
      debugPrint('LocalOcrService: Starting text extraction from path: $imagePath');

      // Read file as bytes
      final file = File(imagePath);
      if (!await file.exists()) {
        throw OcrException('Image file not found: $imagePath');
      }

      final imageBytes = await file.readAsBytes();

      // Use the same method as extractTextFromBytes
      return await extractTextFromBytes(imageBytes);
    } catch (e) {
      debugPrint('LocalOcrService: Text extraction failed - $e');
      throw OcrException('Failed to extract text from image: $e');
    }
  }

  /// Test OCR service with platform info
  Future<bool> testOcrService() async {
    if (!_isInitialized) {
      debugPrint('LocalOcrService: Cannot test - service not initialized');
      return false;
    }

    try {
      // Check if service is available
      final isAvailable = await VisionTextRecognition.isAvailable();
      debugPrint('LocalOcrService: Test - Service available: $isAvailable');

      if (!isAvailable) {
        return false;
      }

      // Get platform info
      final platformInfo = await VisionTextRecognition.getPlatformInfo();
      debugPrint('LocalOcrService: Test - Platform: ${platformInfo.platform}');
      debugPrint('LocalOcrService: Test - Engine: ${platformInfo.engine}');
      debugPrint('LocalOcrService: Test - Engine Version: ${platformInfo.engineVersion}');
      debugPrint('LocalOcrService: Test - Capabilities: ${platformInfo.capabilities}');

      return true;
    } catch (e) {
      debugPrint('LocalOcrService: Test failed - $e');
      return false;
    }
  }

  /// Process multiple images with OCR workflow
  Future<List<OcrTextElement>> processImagesWithOcr({
    required List<String> imageUrls,
    required dynamic extractor,
    Function(String)? onProgress,
  }) async {
    if (!_isInitialized) {
      throw OcrException('OCR service not initialized. Call initialize() first.');
    }

    final allTextElements = <OcrTextElement>[];

    try {
      debugPrint('LocalOcrService: Processing ${imageUrls.length} images with OCR');

      for (int i = 0; i < imageUrls.length; i++) {
        final imageUrl = imageUrls[i];
        onProgress?.call('Processing image ${i + 1}/${imageUrls.length}: ${imageUrl.split('/').last}');

        try {
          // Download image
          Uint8List? imageBytes;
          if (extractor.needsWebViewForImages()) {
            final images = await extractor.downloadImages([imageUrl]);
            if (images.isNotEmpty) {
              imageBytes = images[0];
            }
          } else {
            imageBytes = await extractor.getImageData(imageUrl, useWebView: false);
          }

          if (imageBytes == null) {
            debugPrint("LocalOcrService: Failed to download image: $imageUrl");
            continue;
          }

          debugPrint("LocalOcrService: Successfully downloaded image: $imageUrl (${imageBytes.length} bytes)");

          // Get image size for coordinate conversion
          Size? imageSize;
          try {
            // Try to get image size from extractor if available
            imageSize = await _getImageSizeFromExtractor(imageUrl, extractor);
          } catch (e) {
            debugPrint("LocalOcrService: Could not get image size: $e");
          }

          // Extract text using OCR
          final textElements = await extractTextFromBytes(imageBytes, imageSize: imageSize);
          debugPrint("LocalOcrService: OCR processing completed for $imageUrl: ${textElements.length} text elements found");

          // Group nearby text elements into coherent sentences
          final groupedElements = _groupTextElements(textElements);
          debugPrint("LocalOcrService: Text grouping completed for $imageUrl: ${groupedElements.length} grouped elements (reduced from ${textElements.length})");

          allTextElements.addAll(groupedElements);
        } catch (e) {
          debugPrint("LocalOcrService: Error processing image $imageUrl: $e");
          continue;
        }
      }

      debugPrint("LocalOcrService: OCR workflow completed. Total text elements: ${allTextElements.length}");
      return allTextElements;
    } catch (e) {
      debugPrint("LocalOcrService: OCR workflow failed: $e");
      throw OcrException('OCR workflow failed: $e');
    }
  }

  /// Get image size from image bytes by decoding the image
  Future<Size> _getImageSizeFromBytes(Uint8List imageBytes) async {
    try {
      // Decode the image to get its dimensions
      final ui.Codec codec = await ui.instantiateImageCodec(imageBytes);
      final ui.FrameInfo frameInfo = await codec.getNextFrame();
      final ui.Image image = frameInfo.image;

      final size = Size(image.width.toDouble(), image.height.toDouble());
      image.dispose();
      codec.dispose();

      return size;
    } catch (e) {
      debugPrint('LocalOcrService: Failed to decode image size from bytes: $e');
      throw OcrException('Failed to decode image size: $e');
    }
  }

  /// Get image size from extractor (helper method)
  Future<Size?> _getImageSizeFromExtractor(String imageUrl, dynamic extractor) async {
    try {
      debugPrint('LocalOcrService: Attempting to get image size for: $imageUrl');

      // Use JavaScript to get the actual image dimensions from the DOM
      final String script = '''
        (function() {
          const images = document.querySelectorAll('img');
          for (let i = 0; i < images.length; i++) {
            const img = images[i];
            if (img.src === '$imageUrl' || img.src.includes('${imageUrl.split('/').last}')) {
              return {
                width: img.naturalWidth || img.width || 0,
                height: img.naturalHeight || img.height || 0,
                displayWidth: img.width || 0,
                displayHeight: img.height || 0,
                src: img.src
              };
            }
          }
          return null;
        })();
      ''';

      final result = await extractor.controller.evaluateJavascript(source: script);

      if (result != null && result != 'null') {
        final Map<String, dynamic> imageInfo = Map<String, dynamic>.from(result);
        final double width = (imageInfo['width'] as num?)?.toDouble() ?? 0.0;
        final double height = (imageInfo['height'] as num?)?.toDouble() ?? 0.0;

        if (width > 0 && height > 0) {
          debugPrint('LocalOcrService: Found image size: ${width}x$height for $imageUrl');
          debugPrint('LocalOcrService: Display size: ${imageInfo['displayWidth']}x${imageInfo['displayHeight']}');
          return Size(width, height);
        } else {
          debugPrint('LocalOcrService: Invalid image dimensions: ${width}x$height');
        }
      } else {
        debugPrint('LocalOcrService: No matching image found in DOM for: $imageUrl');
      }

      return null;
    } catch (e) {
      debugPrint('LocalOcrService: Failed to get image size from extractor: $e');
      return null;
    }
  }

  /// Group nearby text elements into coherent sentences
  /// This method merges text elements that are close to each other spatially
  /// to form more complete and readable text blocks
  List<OcrTextElement> _groupTextElements(List<OcrTextElement> elements) {
    if (elements.isEmpty) return elements;

    debugPrint('LocalOcrService: Starting text grouping with ${elements.length} elements');

    // Sort elements by their vertical position (top to bottom), then horizontal (left to right)
    final sortedElements = List<OcrTextElement>.from(elements);
    sortedElements.sort((a, b) {
      // First sort by vertical position (top)
      final verticalDiff = a.boundingBox.top.compareTo(b.boundingBox.top);
      if (verticalDiff.abs() > 10) { // If vertical difference is significant
        return verticalDiff;
      }
      // If elements are on roughly the same line, sort by horizontal position
      return a.boundingBox.left.compareTo(b.boundingBox.left);
    });

    final groupedElements = <OcrTextElement>[];
    final used = <bool>[];

    // Initialize used array
    for (int i = 0; i < sortedElements.length; i++) {
      used.add(false);
    }

    for (int i = 0; i < sortedElements.length; i++) {
      if (used[i]) continue;

      final currentElement = sortedElements[i];
      final group = <OcrTextElement>[currentElement];
      used[i] = true;

      // Look for nearby elements to group with current element
      for (int j = i + 1; j < sortedElements.length; j++) {
        if (used[j]) continue;

        final candidateElement = sortedElements[j];

        // Check if candidate element should be grouped with current group
        if (_shouldGroupElements(group, candidateElement)) {
          group.add(candidateElement);
          used[j] = true;
        }
      }

      // Create merged element from group
      final mergedElement = _mergeTextElements(group);
      groupedElements.add(mergedElement);

      debugPrint('LocalOcrService: Created group with ${group.length} elements: "${mergedElement.text}"');
    }

    debugPrint('LocalOcrService: Text grouping completed. Reduced from ${elements.length} to ${groupedElements.length} elements');
    return groupedElements;
  }

  /// Check if a candidate element should be grouped with existing group
  bool _shouldGroupElements(List<OcrTextElement> group, OcrTextElement candidate) {
    if (group.isEmpty) return false;

    // Calculate the bounding box of the current group
    final groupBounds = _calculateGroupBounds(group);
    final candidateBounds = candidate.boundingBox;

    // Calculate distances and overlaps
    final verticalDistance = _calculateVerticalDistance(groupBounds, candidateBounds);
    final horizontalDistance = _calculateHorizontalDistance(groupBounds, candidateBounds);

    // Calculate average height for threshold calculations
    final avgHeight = (groupBounds.height + candidateBounds.height) / 2;

    // Grouping criteria:
    // 1. Elements on the same line (small vertical distance)
    // 2. Elements in the same column (small horizontal distance and reasonable vertical gap)
    // 3. Elements that are close enough to be part of the same text block

    final isOnSameLine = verticalDistance < avgHeight * 0.3; // Within 30% of average height
    final isInSameColumn = horizontalDistance < avgHeight * 0.5 && verticalDistance < avgHeight * 1.5;
    final isNearby = verticalDistance < avgHeight * 0.8 && horizontalDistance < avgHeight * 2.0;

    // Additional check: ensure text makes sense when combined
    final wouldFormSentence = _wouldFormCoherentText(group, candidate);

    final shouldGroup = (isOnSameLine || isInSameColumn || isNearby) && wouldFormSentence;

    if (shouldGroup) {
      debugPrint('LocalOcrService: Grouping "${candidate.text}" with existing group (vDist: ${verticalDistance.toStringAsFixed(1)}, hDist: ${horizontalDistance.toStringAsFixed(1)})');
    }

    return shouldGroup;
  }

  /// Calculate the bounding box that encompasses all elements in a group
  OcrBoundingBox _calculateGroupBounds(List<OcrTextElement> group) {
    if (group.isEmpty) {
      return const OcrBoundingBox(left: 0, top: 0, right: 0, bottom: 0);
    }

    double minLeft = group.first.boundingBox.left;
    double minTop = group.first.boundingBox.top;
    double maxRight = group.first.boundingBox.right;
    double maxBottom = group.first.boundingBox.bottom;

    for (final element in group) {
      final box = element.boundingBox;
      minLeft = minLeft < box.left ? minLeft : box.left;
      minTop = minTop < box.top ? minTop : box.top;
      maxRight = maxRight > box.right ? maxRight : box.right;
      maxBottom = maxBottom > box.bottom ? maxBottom : box.bottom;
    }

    return OcrBoundingBox(
      left: minLeft,
      top: minTop,
      right: maxRight,
      bottom: maxBottom,
    );
  }

  /// Calculate vertical distance between two bounding boxes
  double _calculateVerticalDistance(OcrBoundingBox box1, OcrBoundingBox box2) {
    // If boxes overlap vertically, distance is 0
    if (box1.bottom >= box2.top && box2.bottom >= box1.top) {
      return 0.0;
    }

    // Calculate gap between boxes
    if (box1.bottom < box2.top) {
      return box2.top - box1.bottom;
    } else {
      return box1.top - box2.bottom;
    }
  }

  /// Calculate horizontal distance between two bounding boxes
  double _calculateHorizontalDistance(OcrBoundingBox box1, OcrBoundingBox box2) {
    // If boxes overlap horizontally, distance is 0
    if (box1.right >= box2.left && box2.right >= box1.left) {
      return 0.0;
    }

    // Calculate gap between boxes
    if (box1.right < box2.left) {
      return box2.left - box1.right;
    } else {
      return box1.left - box2.right;
    }
  }

  /// Check if adding a candidate element would form coherent text
  bool _wouldFormCoherentText(List<OcrTextElement> group, OcrTextElement candidate) {
    // Basic heuristics for text coherence:
    // 1. Avoid grouping very different text sizes (confidence-based)
    // 2. Prefer grouping text that forms logical reading order
    // 3. Avoid grouping isolated single characters with longer text

    final candidateText = candidate.text.trim();
    if (candidateText.isEmpty) return false;

    // Calculate average confidence of the group
    final avgConfidence = group.map((e) => e.confidence).reduce((a, b) => a + b) / group.length;

    // Don't group if confidence is very different (might be noise)
    if ((candidate.confidence - avgConfidence).abs() > 0.3) {
      return false;
    }

    // Don't group single characters with multi-character text unless they're punctuation
    final groupHasMultiChar = group.any((e) => e.text.trim().length > 1);
    final candidateIsSingleChar = candidateText.length == 1;
    final punctuationChars = '.,!?;:()"\'-';
    final candidateIsPunctuation = candidateIsSingleChar && punctuationChars.contains(candidateText);

    if (groupHasMultiChar && candidateIsSingleChar && !candidateIsPunctuation) {
      return false;
    }

    return true;
  }

  /// Merge multiple text elements into a single element
  OcrTextElement _mergeTextElements(List<OcrTextElement> elements) {
    if (elements.isEmpty) {
      throw ArgumentError('Cannot merge empty list of elements');
    }

    if (elements.length == 1) {
      return elements.first;
    }

    // Sort elements by reading order (top-to-bottom, left-to-right)
    final sortedElements = List<OcrTextElement>.from(elements);
    sortedElements.sort((a, b) {
      final verticalDiff = a.boundingBox.top.compareTo(b.boundingBox.top);
      if (verticalDiff.abs() > 10) {
        return verticalDiff;
      }
      return a.boundingBox.left.compareTo(b.boundingBox.left);
    });

    // Combine text with appropriate spacing
    final textParts = <String>[];
    for (int i = 0; i < sortedElements.length; i++) {
      final currentText = sortedElements[i].text.trim();
      if (currentText.isEmpty) continue;

      if (textParts.isNotEmpty) {
        final previousElement = sortedElements[i - 1];
        final currentElement = sortedElements[i];

        // Determine if we need a space between elements
        final needsSpace = _needsSpaceBetweenElements(previousElement, currentElement);
        if (needsSpace) {
          textParts.add(' ');
        }
      }

      textParts.add(currentText);
    }

    final mergedText = textParts.join('');

    // Calculate merged bounding box
    final mergedBounds = _calculateGroupBounds(elements);

    // Calculate average confidence
    final avgConfidence = elements.map((e) => e.confidence).reduce((a, b) => a + b) / elements.length;

    return OcrTextElement(
      text: mergedText,
      boundingBox: mergedBounds,
      confidence: avgConfidence,
    );
  }

  /// Determine if space is needed between two text elements
  bool _needsSpaceBetweenElements(OcrTextElement previous, OcrTextElement current) {
    final prevText = previous.text.trim();
    final currText = current.text.trim();

    if (prevText.isEmpty || currText.isEmpty) return false;

    // Don't add space if previous text ends with punctuation that doesn't need space
    final openingPunctuation = '(["\'\`';
    if (openingPunctuation.contains(prevText[prevText.length - 1])) return false;

    // Don't add space if current text starts with punctuation that doesn't need space
    final closingPunctuation = '.,!?;:)]\"\'\`';
    if (closingPunctuation.contains(currText[0])) return false;

    // Add space for normal text
    return true;
  }

  /// Dispose resources
  Future<void> dispose() async {
    _isInitialized = false;
    debugPrint('LocalOcrService: Disposed');
  }
}

/// Represents a text element recognized by OCR
class OcrTextElement {
  final String text;
  final OcrBoundingBox boundingBox;
  final double confidence;

  const OcrTextElement({
    required this.text,
    required this.boundingBox,
    required this.confidence,
  });

  @override
  String toString() {
    return 'OcrTextElement(text: "$text", boundingBox: $boundingBox, confidence: $confidence)';
  }

  /// Convert to JSON for serialization
  Map<String, dynamic> toJson() {
    return {
      'text': text,
      'boundingBox': boundingBox.toJson(),
      'confidence': confidence,
    };
  }

  /// Create from JSON
  factory OcrTextElement.fromJson(Map<String, dynamic> json) {
    return OcrTextElement(
      text: json['text'] as String,
      boundingBox: OcrBoundingBox.fromJson(json['boundingBox'] as Map<String, dynamic>),
      confidence: (json['confidence'] as num).toDouble(),
    );
  }
}

/// Represents a bounding box for OCR text
class OcrBoundingBox {
  final double left;
  final double top;
  final double right;
  final double bottom;

  const OcrBoundingBox({
    required this.left,
    required this.top,
    required this.right,
    required this.bottom,
  });

  /// Get width of the bounding box
  double get width => right - left;

  /// Get height of the bounding box
  double get height => bottom - top;

  /// Get center point of the bounding box
  Offset get center => Offset(left + width / 2, top + height / 2);

  /// Convert to Rect
  Rect toRect() => Rect.fromLTRB(left, top, right, bottom);

  @override
  String toString() {
    return 'OcrBoundingBox(left: $left, top: $top, right: $right, bottom: $bottom)';
  }

  /// Convert to JSON for serialization
  Map<String, dynamic> toJson() {
    return {
      'left': left,
      'top': top,
      'right': right,
      'bottom': bottom,
    };
  }

  /// Create from JSON
  factory OcrBoundingBox.fromJson(Map<String, dynamic> json) {
    return OcrBoundingBox(
      left: (json['left'] as num).toDouble(),
      top: (json['top'] as num).toDouble(),
      right: (json['right'] as num).toDouble(),
      bottom: (json['bottom'] as num).toDouble(),
    );
  }
}

/// Custom exception for OCR operations
class OcrException implements Exception {
  final String message;
  final dynamic originalError;

  const OcrException(this.message, [this.originalError]);

  @override
  String toString() {
    if (originalError != null) {
      return 'OcrException: $message (Original error: $originalError)';
    }
    return 'OcrException: $message';
  }
}
