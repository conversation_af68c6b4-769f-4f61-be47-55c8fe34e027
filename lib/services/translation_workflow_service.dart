import 'package:flutter/material.dart';
import 'package:imtrans/services/local_ocr_service.dart';
import 'package:imtrans/services/local_translation_service.dart';
import 'package:imtrans/services/webview_overlay_service.dart';
import 'package:imtrans/services/translation_cache_service.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';

/// High-level service that orchestrates the complete translation workflow
/// Coordinates OCR → Translation → Overlay display
class TranslationWorkflowService {
  static final TranslationWorkflowService _instance = TranslationWorkflowService._internal();
  factory TranslationWorkflowService() => _instance;
  TranslationWorkflowService._internal();

  final LocalOcrService _ocrService = LocalOcrService();
  final LocalTranslationService _translationService = LocalTranslationService();
  final WebViewOverlayService _overlayService = WebViewOverlayService();
  final TranslationCacheService _cacheService = TranslationCacheService();

  bool _isInitialized = false;
  bool _isProcessing = false;

  /// Initialize all services
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await _ocrService.initialize();
      await _translationService.initialize();
      await _overlayService.initialize();
      await _cacheService.initialize();

      _isInitialized = true;
      debugPrint('TranslationWorkflowService: All services initialized successfully');
    } catch (e) {
      debugPrint('TranslationWorkflowService: Failed to initialize services - $e');
      throw TranslationWorkflowException('Failed to initialize translation workflow: $e');
    }
  }

  /// Check if services are initialized
  bool get isInitialized => _isInitialized;

  /// Check if currently processing
  bool get isProcessing => _isProcessing;

  /// Test if all services are available and working
  Future<bool> testServices() async {
    if (!_isInitialized) return false;

    try {
      final ocrTest = await _ocrService.testOcrService();
      debugPrint('TranslationWorkflowService: OCR service test result: $ocrTest');
      return ocrTest;
    } catch (e) {
      debugPrint('TranslationWorkflowService: Service test failed - $e');
      return false;
    }
  }

  /// Set the WebView controller for overlay service
  void setWebViewController(InAppWebViewController? controller) {
    _overlayService.setController(controller);
  }

  /// Process a single image with caching support (for lazy loading)
  Future<TranslationWorkflowResult> processSingleImage({
    required String imageUrl,
    required dynamic extractor,
    Function(String)? onProgress,
    String? loadingText,
  }) async {
    if (!_isInitialized) {
      throw TranslationWorkflowException('Translation workflow not initialized');
    }

    try {
      onProgress?.call('Checking cache for image...');

      // Check cache first
      final cachedResult = await _cacheService.getCachedResult(imageUrl);
      if (cachedResult != null) {
        debugPrint('TranslationWorkflowService: Using cached result for $imageUrl');

        // Display cached overlays
        await _overlayService.showTranslationOverlays(
          translatedElements: cachedResult.translatedElements,
          imageUrls: [imageUrl],
        );

        return TranslationWorkflowResult(
          success: true,
          message: 'Translation loaded from cache',
          textElementsCount: cachedResult.translatedElements.length,
          translatedElements: cachedResult.translatedElements,
        );
      }

      onProgress?.call('Processing image with OCR...');

      // Show loading indicator
      await _overlayService.showLoadingIndicator(imageUrl, loadingText: loadingText ?? 'Translating...');

      // Process with OCR
      final textElements = await _ocrService.processImagesWithOcr(
        imageUrls: [imageUrl],
        extractor: extractor,
        onProgress: onProgress,
      );

      if (textElements.isEmpty) {
        await _overlayService.hideAllLoadingIndicators();
        await _overlayService.hideLoadingIndicator(imageUrl);
        return TranslationWorkflowResult(
          success: false,
          message: 'No text found in image',
          textElementsCount: 0,
        );
      }

      onProgress?.call('Translating text elements...');

      // Translate text elements
      final translatedElements = await _translationService.translateOcrElements(textElements, onProgress: onProgress);

      if (translatedElements.isEmpty) {
        await _overlayService.hideAllLoadingIndicators();
        await _overlayService.hideLoadingIndicator(imageUrl);
        return TranslationWorkflowResult(
          success: false,
          message: 'Translation failed for all text elements',
          textElementsCount: textElements.length,
        );
      }

      onProgress?.call('Displaying translation overlays...');

      // Display overlays (this will also hide loading indicator)
      await _overlayService.showTranslationOverlays(
        translatedElements: translatedElements,
        imageUrls: [imageUrl],
      );

      // Force hide all loading indicators after successful overlay creation
      await _overlayService.hideAllLoadingIndicators();

      // Also specifically hide for this image URL
      await _overlayService.hideLoadingIndicator(imageUrl);

      // Cache the result
      await _cacheService.cacheResult(imageUrl, translatedElements);

      return TranslationWorkflowResult(
        success: true,
        message: 'Translation completed successfully',
        textElementsCount: translatedElements.length,
        translatedElements: translatedElements,
      );

    } catch (e) {
      // Always ensure all loading indicators are hidden on error
      try {
        await _overlayService.hideAllLoadingIndicators();
        await _overlayService.hideLoadingIndicator(imageUrl);
      } catch (hideError) {
        debugPrint('TranslationWorkflowService: Failed to hide loading indicators - $hideError');
      }
      debugPrint('TranslationWorkflowService: Single image processing failed - $e');
      return TranslationWorkflowResult(
        success: false,
        message: 'Translation workflow failed: ${e.toString()}',
        textElementsCount: 0,
      );
    }
  }

  /// Execute complete translation workflow: OCR → Translation → Overlay
  Future<TranslationWorkflowResult> executeTranslationWorkflow({
    required List<String> imageUrls,
    required dynamic extractor,
    Function(String)? onProgress,
  }) async {
    if (!_isInitialized) {
      throw TranslationWorkflowException('Translation workflow not initialized');
    }

    if (_isProcessing) {
      throw TranslationWorkflowException('Translation workflow already in progress');
    }

    _isProcessing = true;
    
    try {
      onProgress?.call('Starting OCR processing...');
      
      // Step 1: Process images with OCR
      final allTextElements = await _ocrService.processImagesWithOcr(
        imageUrls: imageUrls,
        extractor: extractor,
        onProgress: onProgress,
      );

      if (allTextElements.isEmpty) {
        return TranslationWorkflowResult(
          success: false,
          message: 'No text found in images',
          textElementsCount: 0,
        );
      }

      onProgress?.call('Translating ${allTextElements.length} text elements...');

      // Step 2: Translate text elements
      final translatedElements = await _translationService.translateOcrElements(allTextElements, onProgress: onProgress);

      if (translatedElements.isEmpty) {
        return TranslationWorkflowResult(
          success: false,
          message: 'Translation failed for all text elements',
          textElementsCount: allTextElements.length,
        );
      }

      onProgress?.call('Displaying translation overlays...');

      // Step 3: Display overlays
      await _overlayService.showTranslationOverlays(
        translatedElements: translatedElements,
        imageUrls: imageUrls,
      );

      return TranslationWorkflowResult(
        success: true,
        message: 'Translation completed successfully',
        textElementsCount: translatedElements.length,
        translatedElements: translatedElements,
      );

    } catch (e) {
      debugPrint('TranslationWorkflowService: Workflow execution failed - $e');
      return TranslationWorkflowResult(
        success: false,
        message: 'Translation workflow failed: ${e.toString()}',
        textElementsCount: 0,
      );
    } finally {
      _isProcessing = false;
    }
  }

  /// Hide all translation overlays
  Future<void> hideTranslationOverlays() async {
    if (!_isInitialized) return;
    
    try {
      await _overlayService.hideOverlays();
    } catch (e) {
      debugPrint('TranslationWorkflowService: Failed to hide overlays - $e');
    }
  }

  /// Check if overlays are currently active
  bool get overlaysActive => _overlayService.overlaysActive;

  /// Cleanup all overlays and reset state
  Future<void> cleanup() async {
    if (!_isInitialized) return;

    try {
      await _overlayService.cleanup();
      _isProcessing = false;
    } catch (e) {
      debugPrint('TranslationWorkflowService: Cleanup failed - $e');
    }
  }

  /// Initialize lazy processing system
  Future<void> initializeLazyProcessing() async {
    if (!_isInitialized) {
      throw TranslationWorkflowException('Translation workflow not initialized');
    }

    try {
      await _overlayService.initializeOverlaySystem();
      debugPrint('TranslationWorkflowService: Lazy processing system initialized');
    } catch (e) {
      debugPrint('TranslationWorkflowService: Failed to initialize lazy processing - $e');
      throw TranslationWorkflowException('Failed to initialize lazy processing: $e');
    }
  }

  /// Check if image is already cached
  Future<bool> isImageCached(String imageUrl) async {
    if (!_isInitialized) return false;

    try {
      final cachedResult = await _cacheService.getCachedResult(imageUrl);
      return cachedResult != null;
    } catch (e) {
      debugPrint('TranslationWorkflowService: Failed to check cache status - $e');
      return false;
    }
  }

  /// Clear cache for specific image
  Future<void> clearImageCache(String imageUrl) async {
    if (!_isInitialized) return;

    try {
      await _cacheService.cacheResult(imageUrl, []); // Clear by setting empty result
      await _overlayService.clearImageCache(imageUrl);
      debugPrint('TranslationWorkflowService: Cache cleared for $imageUrl');
    } catch (e) {
      debugPrint('TranslationWorkflowService: Failed to clear image cache - $e');
    }
  }

  /// Clear all translation cache
  Future<void> clearAllCache() async {
    if (!_isInitialized) return;

    try {
      await _cacheService.clearAllCache();
      await _overlayService.clearAllCache();
      debugPrint('TranslationWorkflowService: All cache cleared');
    } catch (e) {
      debugPrint('TranslationWorkflowService: Failed to clear all cache - $e');
    }
  }

  /// Get cache statistics
  Future<CacheStatistics> getCacheStatistics() async {
    if (!_isInitialized) {
      throw TranslationWorkflowException('Translation workflow not initialized');
    }

    try {
      return await _cacheService.getCacheStatistics();
    } catch (e) {
      debugPrint('TranslationWorkflowService: Failed to get cache statistics - $e');
      throw TranslationWorkflowException('Failed to get cache statistics: $e');
    }
  }

  /// Dispose all services
  Future<void> dispose() async {
    try {
      await _ocrService.dispose();
      await _translationService.dispose();
      _overlayService.dispose();
      _cacheService.dispose();

      _isInitialized = false;
      _isProcessing = false;
      debugPrint('TranslationWorkflowService: All services disposed');
    } catch (e) {
      debugPrint('TranslationWorkflowService: Error during disposal - $e');
    }
  }

  /// Update action button state for a specific image
  Future<void> updateActionButtonState(String imageUrl, String state) async {
    await _overlayService.updateActionButtonState(imageUrl, state);
  }

  /// Set translation mode (enable/disable action buttons)
  Future<void> setTranslationMode(bool enabled, {String? buttonText}) async {
    await _overlayService.setTranslationMode(enabled, buttonText: buttonText);
  }

  /// Hide loading indicator for a specific image
  Future<void> hideLoadingIndicator(String imageUrl) async {
    await _overlayService.hideLoadingIndicator(imageUrl);
  }

  /// Force hide all loading indicators (cleanup function)
  Future<void> hideAllLoadingIndicators() async {
    await _overlayService.hideAllLoadingIndicators();
  }

  /// Debug loading indicators status
  Future<void> debugLoadingIndicators() async {
    await _overlayService.debugLoadingIndicators();
  }
}

/// Result of translation workflow execution
class TranslationWorkflowResult {
  final bool success;
  final String message;
  final int textElementsCount;
  final List<OcrTextElement>? translatedElements;

  const TranslationWorkflowResult({
    required this.success,
    required this.message,
    required this.textElementsCount,
    this.translatedElements,
  });

  @override
  String toString() {
    return 'TranslationWorkflowResult(success: $success, message: $message, textElementsCount: $textElementsCount)';
  }
}

/// Custom exception for translation workflow operations
class TranslationWorkflowException implements Exception {
  final String message;
  final dynamic originalError;

  const TranslationWorkflowException(this.message, [this.originalError]);

  @override
  String toString() {
    if (originalError != null) {
      return 'TranslationWorkflowException: $message (Original error: $originalError)';
    }
    return 'TranslationWorkflowException: $message';
  }
}
