import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:crypto/crypto.dart';
import 'local_ocr_service.dart';

/// Service for caching translation results to avoid redundant OCR and translation operations
class TranslationCacheService {
  static final TranslationCacheService _instance = TranslationCacheService._internal();
  factory TranslationCacheService() => _instance;
  TranslationCacheService._internal();

  static const String _cacheKeyPrefix = 'translation_cache_';
  static const String _cacheMetaKey = 'translation_cache_meta';
  static const int _maxCacheSize = 100; // Maximum number of cached items
  static const int _cacheExpiryDays = 7; // Cache expiry in days

  SharedPreferences? _prefs;
  bool _isInitialized = false;

  /// Initialize the cache service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      _prefs = await SharedPreferences.getInstance();
      _isInitialized = true;
      
      // Clean up expired cache entries
      await _cleanupExpiredEntries();
      
      debugPrint('TranslationCacheService: Initialized successfully');
    } catch (e) {
      debugPrint('TranslationCacheService: Failed to initialize - $e');
      throw CacheException('Failed to initialize translation cache: $e');
    }
  }

  /// Generate cache key for an image URL
  String _generateCacheKey(String imageUrl) {
    final bytes = utf8.encode(imageUrl);
    final digest = sha256.convert(bytes);
    return _cacheKeyPrefix + digest.toString();
  }

  /// Get cached translation result for an image
  Future<CachedTranslationResult?> getCachedResult(String imageUrl) async {
    if (!_isInitialized || _prefs == null) {
      throw CacheException('Cache service not initialized');
    }

    try {
      final cacheKey = _generateCacheKey(imageUrl);
      final cachedData = _prefs!.getString(cacheKey);
      
      if (cachedData == null) {
        debugPrint('TranslationCacheService: No cache found for $imageUrl');
        return null;
      }

      final Map<String, dynamic> data = jsonDecode(cachedData);
      final result = CachedTranslationResult.fromJson(data);
      
      // Check if cache is expired
      if (result.isExpired()) {
        debugPrint('TranslationCacheService: Cache expired for $imageUrl');
        await _removeCacheEntry(cacheKey);
        return null;
      }

      debugPrint('TranslationCacheService: Cache hit for $imageUrl');
      return result;
    } catch (e) {
      debugPrint('TranslationCacheService: Failed to get cached result - $e');
      return null;
    }
  }

  /// Cache translation result for an image
  Future<void> cacheResult(String imageUrl, List<OcrTextElement> translatedElements) async {
    if (!_isInitialized || _prefs == null) {
      throw CacheException('Cache service not initialized');
    }

    try {
      final cacheKey = _generateCacheKey(imageUrl);
      final result = CachedTranslationResult(
        imageUrl: imageUrl,
        translatedElements: translatedElements,
        timestamp: DateTime.now(),
      );

      await _prefs!.setString(cacheKey, jsonEncode(result.toJson()));
      await _updateCacheMeta(cacheKey);
      
      debugPrint('TranslationCacheService: Cached result for $imageUrl with ${translatedElements.length} elements');
    } catch (e) {
      debugPrint('TranslationCacheService: Failed to cache result - $e');
      throw CacheException('Failed to cache translation result: $e');
    }
  }

  /// Update cache metadata for size management
  Future<void> _updateCacheMeta(String cacheKey) async {
    try {
      final metaData = _prefs!.getString(_cacheMetaKey);
      List<String> cacheKeys = [];
      
      if (metaData != null) {
        final List<dynamic> keys = jsonDecode(metaData);
        cacheKeys = keys.cast<String>();
      }

      // Add new key if not exists
      if (!cacheKeys.contains(cacheKey)) {
        cacheKeys.add(cacheKey);
      }

      // Remove oldest entries if cache size exceeds limit
      while (cacheKeys.length > _maxCacheSize) {
        final oldestKey = cacheKeys.removeAt(0);
        await _removeCacheEntry(oldestKey);
      }

      await _prefs!.setString(_cacheMetaKey, jsonEncode(cacheKeys));
    } catch (e) {
      debugPrint('TranslationCacheService: Failed to update cache meta - $e');
    }
  }

  /// Remove a cache entry
  Future<void> _removeCacheEntry(String cacheKey) async {
    try {
      await _prefs!.remove(cacheKey);
      debugPrint('TranslationCacheService: Removed cache entry $cacheKey');
    } catch (e) {
      debugPrint('TranslationCacheService: Failed to remove cache entry - $e');
    }
  }

  /// Clean up expired cache entries
  Future<void> _cleanupExpiredEntries() async {
    try {
      final metaData = _prefs!.getString(_cacheMetaKey);
      if (metaData == null) return;

      final List<dynamic> keys = jsonDecode(metaData);
      final List<String> cacheKeys = keys.cast<String>();
      final List<String> validKeys = [];

      for (final key in cacheKeys) {
        final cachedData = _prefs!.getString(key);
        if (cachedData != null) {
          try {
            final Map<String, dynamic> data = jsonDecode(cachedData);
            final result = CachedTranslationResult.fromJson(data);
            
            if (!result.isExpired()) {
              validKeys.add(key);
            } else {
              await _removeCacheEntry(key);
            }
          } catch (e) {
            // Remove invalid cache entries
            await _removeCacheEntry(key);
          }
        }
      }

      await _prefs!.setString(_cacheMetaKey, jsonEncode(validKeys));
      debugPrint('TranslationCacheService: Cleaned up expired entries');
    } catch (e) {
      debugPrint('TranslationCacheService: Failed to cleanup expired entries - $e');
    }
  }

  /// Clear all cache
  Future<void> clearAllCache() async {
    if (!_isInitialized || _prefs == null) {
      throw CacheException('Cache service not initialized');
    }

    try {
      final metaData = _prefs!.getString(_cacheMetaKey);
      if (metaData != null) {
        final List<dynamic> keys = jsonDecode(metaData);
        final List<String> cacheKeys = keys.cast<String>();
        
        for (final key in cacheKeys) {
          await _prefs!.remove(key);
        }
      }
      
      await _prefs!.remove(_cacheMetaKey);
      debugPrint('TranslationCacheService: All cache cleared');
    } catch (e) {
      debugPrint('TranslationCacheService: Failed to clear all cache - $e');
      throw CacheException('Failed to clear cache: $e');
    }
  }

  /// Get cache statistics
  Future<CacheStatistics> getCacheStatistics() async {
    if (!_isInitialized || _prefs == null) {
      throw CacheException('Cache service not initialized');
    }

    try {
      final metaData = _prefs!.getString(_cacheMetaKey);
      int totalEntries = 0;
      int validEntries = 0;
      int expiredEntries = 0;

      if (metaData != null) {
        final List<dynamic> keys = jsonDecode(metaData);
        final List<String> cacheKeys = keys.cast<String>();
        totalEntries = cacheKeys.length;

        for (final key in cacheKeys) {
          final cachedData = _prefs!.getString(key);
          if (cachedData != null) {
            try {
              final Map<String, dynamic> data = jsonDecode(cachedData);
              final result = CachedTranslationResult.fromJson(data);
              
              if (result.isExpired()) {
                expiredEntries++;
              } else {
                validEntries++;
              }
            } catch (e) {
              expiredEntries++;
            }
          }
        }
      }

      return CacheStatistics(
        totalEntries: totalEntries,
        validEntries: validEntries,
        expiredEntries: expiredEntries,
        maxCacheSize: _maxCacheSize,
      );
    } catch (e) {
      debugPrint('TranslationCacheService: Failed to get cache statistics - $e');
      throw CacheException('Failed to get cache statistics: $e');
    }
  }

  /// Dispose the service
  void dispose() {
    _isInitialized = false;
    _prefs = null;
    debugPrint('TranslationCacheService: Disposed');
  }
}

/// Represents a cached translation result
class CachedTranslationResult {
  final String imageUrl;
  final List<OcrTextElement> translatedElements;
  final DateTime timestamp;

  const CachedTranslationResult({
    required this.imageUrl,
    required this.translatedElements,
    required this.timestamp,
  });

  /// Check if the cache entry is expired
  bool isExpired() {
    final now = DateTime.now();
    final expiryDate = timestamp.add(const Duration(days: TranslationCacheService._cacheExpiryDays));
    return now.isAfter(expiryDate);
  }

  /// Convert to JSON for storage
  Map<String, dynamic> toJson() {
    return {
      'imageUrl': imageUrl,
      'translatedElements': translatedElements.map((e) => e.toJson()).toList(),
      'timestamp': timestamp.toIso8601String(),
    };
  }

  /// Create from JSON
  factory CachedTranslationResult.fromJson(Map<String, dynamic> json) {
    return CachedTranslationResult(
      imageUrl: json['imageUrl'] as String,
      translatedElements: (json['translatedElements'] as List)
          .map((e) => OcrTextElement.fromJson(e as Map<String, dynamic>))
          .toList(),
      timestamp: DateTime.parse(json['timestamp'] as String),
    );
  }
}

/// Cache statistics
class CacheStatistics {
  final int totalEntries;
  final int validEntries;
  final int expiredEntries;
  final int maxCacheSize;

  const CacheStatistics({
    required this.totalEntries,
    required this.validEntries,
    required this.expiredEntries,
    required this.maxCacheSize,
  });

  double get hitRate => totalEntries > 0 ? validEntries / totalEntries : 0.0;
  double get usagePercentage => maxCacheSize > 0 ? totalEntries / maxCacheSize : 0.0;

  @override
  String toString() {
    return 'CacheStatistics(total: $totalEntries, valid: $validEntries, expired: $expiredEntries, usage: ${(usagePercentage * 100).toStringAsFixed(1)}%)';
  }
}

/// Exception thrown by cache operations
class CacheException implements Exception {
  final String message;
  const CacheException(this.message);

  @override
  String toString() => 'CacheException: $message';
}
